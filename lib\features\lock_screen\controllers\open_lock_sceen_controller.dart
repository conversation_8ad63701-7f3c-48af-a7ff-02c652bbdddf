import 'dart:async';

import 'package:get/get.dart';

import '../../../core/routes/app_pages.dart';
import '../../settings/presentation/getx/controllers/lock_screen_timer_controller.dart';

class OpenLockScreenController extends GetxController {
  final LockScreenTimerController lockScreenTimerController =
      Get.find<LockScreenTimerController>();
  Duration get timeoutDuration =>
      Duration(minutes: lockScreenTimerController.timer.value);

  Timer? _inactivityTimer;

  void initializeTimer() {
    _resetTimer();
  }

  void _resetTimer() {
    _inactivityTimer?.cancel();
    _inactivityTimer = Timer(timeoutDuration, _onTimeout);
  }

  void _onTimeout() {
    Get.toNamed(
      Routes.lockScreen,
    );
  }

  void userInteractionDetected() {
    _resetTimer();
  }

  @override
  void onClose() {
    _inactivityTimer?.cancel();
    super.onClose();
  }
}
