import 'dart:async';

import 'package:get/get.dart';

import '../../../core/routes/app_pages.dart';

class OpenLockScreenController extends GetxController {
  static const Duration timeoutDuration = Duration(minutes: 15);

  Timer? _inactivityTimer;

  void initializeTimer() {
    _resetTimer();
  }

  void _resetTimer() {
    _inactivityTimer?.cancel();
    _inactivityTimer = Timer(timeoutDuration, _onTimeout);
  }

  void _onTimeout() {
    Get.toNamed(
      Routes.lockScreen,
    );
  }

  void userInteractionDetected() {
    _resetTimer();
  }

  @override
  void onClose() {
    _inactivityTimer?.cancel();
    super.onClose();
  }
}
