import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/core/utils/app_colors.dart';
import 'package:point_of_sale/core/utils/app_text_style.dart';
import 'package:point_of_sale/core/widgets/failed_snack_bar.dart';
import 'package:point_of_sale/features/settings/presentation/getx/controllers/printers_ip_address_controller.dart';

import '../../../../../core/utils/size_config.dart';

class AddPrinterSection extends GetView<PrintersIPAddressController> {
  const AddPrinterSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: AppSize.height(12),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              focusNode: controller.ipFocusNode,
              onSubmitted: (value) {
                final ip = controller.ipController.text.trim();
                if (controller.isValidIP(ip)) {
                  controller.addPrinterIP(ip);
                  controller.ipController.clear();
                } else {
                  failedSnaskBar('pleaseEnterAvalidIPAddress'.tr);
                }
              },
              onChanged: (value) {
                if (value.isEmpty) {
                  controller.ipFocusNode.unfocus();
                }
              },
              onEditingComplete: () {
                final ip = controller.ipController.text.trim();
                if (controller.isValidIP(ip)) {
                  controller.addPrinterIP(ip);
                  controller.ipController.clear();
                } else {
                  failedSnaskBar('pleaseEnterAvalidIPAddress'.tr);
                }
              },
              onTapOutside: (event) {
                controller.ipFocusNode.unfocus();
              },
              controller: controller.ipController,
              decoration: InputDecoration(
                hintText: 'printerIPAddress'.tr,
                border: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: AppColors.lavenderGray,
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: AppColors.primaryColor,
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              keyboardType: TextInputType.number,
            ),
          ),
          SizedBox(width: AppSize.width(12)),
          InkWell(
            onTap: () {
              final ip = controller.ipController.text.trim();
              if (controller.isValidIP(ip)) {
                controller.addPrinterIP(ip);
                controller.ipController.clear();
              } else {
                failedSnaskBar('pleaseEnterAvalidIPAddress'.tr);
              }
            },
            child: Container(
              height: AppSize.height(47),
              width: AppSize.width(112),
              decoration: BoxDecoration(
                color: AppColors.primaryColor,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: Text(
                  'add'.tr,
                  style: AppTextStyle.white18800,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
