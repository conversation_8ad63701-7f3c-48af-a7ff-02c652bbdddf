import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:point_of_sale/core/utils/app_text_style.dart';
import 'package:point_of_sale/core/utils/size_config.dart';

import '../../../../../core/utils/app_colors.dart';
import '../../getx/controllers/lock_screen_timer_controller.dart';

class LockeScreenSection extends GetView<LockScreenTimerController> {
  const LockeScreenSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: AppSize.height(47),
      padding: EdgeInsets.symmetric(
        horizontal: AppSize.width(16),
        vertical: AppSize.height(12),
      ),
      margin: EdgeInsets.symmetric(
        horizontal: AppSize.width(16),
        vertical: AppSize.height(12),
      ),
      decoration: BoxDecoration(
        color: AppColors.white,
        border: Border.all(
          color: AppColors.lavenderGray,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'lockScreen'.tr,
            style: AppTextStyle.primary16800,
          ),
          const Spacer(),
          IconButton(
            padding: EdgeInsets.zero,
            onPressed: () {
              controller.countDown();
            },
            icon: Icon(
              Icons.remove,
              color: AppColors.primaryColor,
              size: AppSize.height(24),
            ),
          ),
          Obx(
            () => Text(
              '${controller.timer.value} ${'minutes'.tr}',
              style: AppTextStyle.primary16800,
            ),
          ),
          IconButton(
            padding: EdgeInsets.zero,
            onPressed: () {
              controller.countUp();
            },
            icon: Icon(
              Icons.add,
              color: AppColors.primaryColor,
              size: AppSize.height(24),
            ),
          ),
        ],
      ),
    );
  }
}
