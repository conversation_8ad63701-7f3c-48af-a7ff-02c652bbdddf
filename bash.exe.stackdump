Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFB740, 0007FFFFA640) msys-2.0.dll+0x1FEBA
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210285FF9, 0007FFFFB5F8, 0007FFFFB740, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFB740  0002100690B4 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBA20  00021006A49D (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF0B730000 ntdll.dll
7FFF0B100000 KERNEL32.DLL
7FFF08F30000 KERNELBASE.dll
7FFF0B530000 USER32.dll
7FFF089D0000 win32u.dll
000210040000 msys-2.0.dll
7FFF0B400000 GDI32.dll
7FFF08820000 gdi32full.dll
7FFF08BE0000 msvcp_win.dll
7FFF08AC0000 ucrtbase.dll
7FFF09A80000 advapi32.dll
7FFF09F90000 msvcrt.dll
7FFF09B40000 sechost.dll
7FFF09310000 bcrypt.dll
7FFF0B2E0000 RPCRT4.dll
7FFF080A0000 CRYPTBASE.DLL
7FFF09340000 bcryptPrimitives.dll
7FFF093C0000 IMM32.DLL
